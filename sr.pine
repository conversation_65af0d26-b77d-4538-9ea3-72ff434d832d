//@version=6
// ====================================================================
// BrandonJames - S/R Pro v4.6
// ====================================================================

indicator("BrandonJames - S/R Pro v4.6.6", shorttitle="[BrandonJames] S/R Pro v4.6.6", overlay=true, max_bars_back=5000)

// ====================================================================
// INPUT PARAMETERS - ORGANIZED BY FUNCTIONALITY
// ====================================================================

// ----- Core Settings -----
group_core = "⚙️ Core Settings"
max_blocks  = input.int(7, "📦 Max Blocks Displayed", minval=1, maxval=20, group=group_core, tooltip="Maximum number of order blocks displayed on chart")
analysis_blocks = input.int(15, "🔍 Analysis Blocks", minval=5, maxval=50, group=group_core, tooltip="Number of blocks to keep in memory for analysis (higher values improve detection quality)")
star_selection = input.string("⭐⭐⭐", "⭐ Min Star Rating", options=["⭐", "⭐⭐", "⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐⭐⭐"], group=group_core, tooltip="Filter orderblocks by minimum star rating (1-5 stars)")
min_star_rating = switch star_selection
    "⭐" => 1
    "⭐⭐" => 2
    "⭐⭐⭐" => 3
    "⭐⭐⭐⭐" => 4
    "⭐⭐⭐⭐⭐" => 5
extendObs   = input.int(10, "➡️ Extend Right", minval=1, maxval=50, group=group_core, tooltip="Extend orderblocks to right after price")
debugMode   = input.bool(false, "  Debug Mode", group=group_core)

// ----- Lookback Settings -----
group_lookback = "🔍 Lookback Settings"
lookbackPeriod     = input.int(6, "🎯 Lookback Period", minval=2, group=group_lookback, tooltip="Number of bars for pivot calculation")
dynamicATRPeriod   = input.int(14, "ATR Period", group=group_lookback, tooltip="ATR period used for calculations")

// ----- Lookback Optimization -----
group_lookback_opt = "🔧 Lookback Optimization"
enableLookbackOptimization = input.bool(false, "🚀 Enable Lookback Optimization", group=group_lookback_opt, tooltip="Automatically find the best lookback period based on orderblock strength")
minLookbackRange = input.int(5, "📉 Min Lookback Range", minval=2, maxval=15, group=group_lookback_opt, tooltip="Minimum lookback period to test")
maxLookbackRange = input.int(20, "📈 Max Lookback Range", minval=10, maxval=50, group=group_lookback_opt, tooltip="Maximum lookback period to test")
maxBlocksPerLookback = input.int(5, "📦 Max Blocks Per Test", minval=3, maxval=10, group=group_lookback_opt, tooltip="Maximum orderblocks to consider per lookback period")
showOptimizedValue = input.bool(true, "📊 Show Optimized Value", group=group_lookback_opt, tooltip="Display which lookback value was selected")

// ----- Volume Settings -----
group_volume         = "📊 Volume Settings"
volMultiplier        = input.float(1.2, "📉 Threshold", minval=0.1, step=0.1, group=group_volume, tooltip="Volume significance multiplier")
buySellDominanceThreshold = input.float(60.0, "🔵🔴 Buy/Sell Dominance %", minval=50, maxval=100, step=1, group=group_volume, tooltip="Threshold to determine buy/sell dominance")
vol_len              = input.int(2, "📈 Smoothing", minval=1, group=group_volume, tooltip="Bars for volume filtering")
volumeSpikeFactor    = input.float(2.0, "🚀 Volume Spike Factor", minval=1.5, maxval=5.0, step=0.1, group=group_volume, tooltip="Minimum volume spike required at pivot formation")
checkVolumeSpike     = input.bool(false, "✅ Require Volume Spike", group=group_volume, tooltip="Only create order blocks when pivot forms with significant volume")
volumeSpikeWindow    = input.int(3, "📊 Volume Spike Window", minval=1, maxval=10, group=group_volume, tooltip="Number of bars to check for volume spike around pivot")

// ----- Block Appearance -----
group_block_appearance = "📏 Block Appearance"
showPriceLabels      = input.bool(true, "🏷️ Price Labels", group=group_block_appearance)
showMidline          = input.bool(true, "➖ Show Midlines", group=group_block_appearance)
useDynamicBox        = input.bool(true, "🔄 Dynamic Size", group=group_block_appearance, tooltip="Adjust width based on volatility")
adaptiveBoxSize      = input.bool(true, "📊 Adaptive Size", group=group_block_appearance, tooltip="Automatically adjust box size based on market conditions")
box_width            = input.float(1, "📏 Base Width", minval=0, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for box size")
dynamicBoxMultiplier = input.float(1.0, "🌀 Multiplier", minval=0.1, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for dynamic width")
minBoxSize           = input.float(0.5, "🔻 Min Size", minval=0.1, step=0.1, group=group_block_appearance, tooltip="Minimum box size as ATR multiplier")
maxBoxSize           = input.float(8.0, "🔺 Max Size", minval=1.0, step=0.5, group=group_block_appearance, tooltip="Maximum box size as ATR multiplier")
boxSizeVolatilityWeight = input.float(0.7, "📈 Volatility Weight", minval=0.0, maxval=1.0, step=0.1, group=group_block_appearance, tooltip="Weight of volatility in dynamic box size calculation")
midlineColor         = input.color(color.rgb(255,255,255,70), "Midline Color", group=group_block_appearance)

// ----- Block Behavior -----
group_block_behavior = "⚙️ Block Behavior"
hideOverlaps         = input.bool(true, "🚫 Hide Overlapping", group=group_block_behavior)
showBreakerBlocks    = input.bool(true, "💥 Show Broken", group=group_block_behavior)
showMitigated        = input.bool(false, "⚡ Show Mitigated", group=group_block_behavior)
useTrendFilter       = input.bool(false, "📈 Use Trend Filter (EMA200)", group=group_block_behavior, tooltip="Only create blocks if trend conditions are met: supports allowed when price is above EMA200, resistances when below EMA200")

// ----- Block Colors -----
group_block_colors = "🎨 Block Colors"
bullColor        = input.color(color.rgb(0,180,108,75), "🟢 Bullish Fill", group=group_block_colors)
bearColor        = input.color(color.rgb(255,82,82,75), "🔴 Bearish Fill", group=group_block_colors)
breakoutColor    = input.color(color.rgb(255,215,0,75), "💥 Broken Fill", group=group_block_colors)
mitigatedColor   = input.color(color.rgb(128,128,128,85), "⚡ Mitigated Fill", group=group_block_colors)

// Enhanced Border Colors
bullBorderColor  = input.color(color.rgb(0,220,128), "🟢 Bullish Border", group=group_block_colors)
bearBorderColor  = input.color(color.rgb(255,100,100), "🔴 Bearish Border", group=group_block_colors)
brokenBorderColor = input.color(color.rgb(255,165,0), "💥 Broken Border", group=group_block_colors)
mitigatedBorderColor = input.color(color.rgb(160,160,160), "⚡ Mitigated Border", group=group_block_colors)

// Advanced Color Options
enhancedColorMode = input.bool(true, "Enhanced Color Mode", group=group_block_colors, tooltip="Use advanced color gradients and effects")
strengthBasedColors = input.bool(true, "Strength-Based Colors", group=group_block_colors, tooltip="Adjust color intensity based on zone strength")
volumeBasedBorders = input.bool(true, "Volume-Based Borders", group=group_block_colors, tooltip="Thicker borders for high-volume zones")

// ----- Visual Enhancements -----
group_visual            = "🎨 Visual Enhancements"

// Theme Presets
themePreset = input.string("Professional", "🎨 Theme Preset", options=["Professional", "Dark", "Neon", "Minimal", "Colorblind", "Custom"], group=group_visual, tooltip="Pre-configured color themes")

// Enhanced Block Styling
blockStyle = input.string("Standard", "📦 Block Style", options=["Standard", "Gradient", "3D Shadow", "Rounded", "Glow"], group=group_visual, tooltip="Visual style for order blocks")
gradientIntensity = input.float(0.3, "🌈 Gradient Intensity", minval=0.0, maxval=1.0, step=0.1, group=group_visual, tooltip="Strength of gradient effect")
shadowOffset = input.int(2, "🔳 Shadow Offset", minval=0, maxval=10, group=group_visual, tooltip="Shadow distance for 3D effect")
glowEffect = input.bool(true, "✨ Glow Effect", group=group_visual, tooltip="Add subtle glow around important blocks")

// Smart Label System
smartLabels = input.bool(true, "🧠 Smart Labels", group=group_visual, tooltip="Intelligent label positioning to avoid overlaps")
iconLabels = input.bool(true, "🔣 Icon Labels", group=group_visual, tooltip="Use icons instead of text for cleaner look")
multiLineTooltips = input.bool(true, "📝 Rich Tooltips", group=group_visual, tooltip="Detailed hover information")
labelAnimation = input.bool(true, "🎬 Label Animation", group=group_visual, tooltip="Subtle animations for new labels")

// Display Options
displayModeOpt          = input.string("Compact", "Display Mode", options=["Compact", "Detailed", "Minimal", "Full"], group=group_visual, tooltip="Information density level")
orderBlockTextSizeOpt   = input.string("normal", "Block Text Size", options=["tiny", "small", "normal", "large"], group=group_visual)
orderBlockTextSize      = orderBlockTextSizeOpt == "tiny" ? size.tiny : orderBlockTextSizeOpt == "small" ? size.small : orderBlockTextSizeOpt == "normal" ? size.normal : size.large
orderBlockBorderStyleOpt = input.string("solid", "Border Style", options=["solid", "dashed", "dotted"], group=group_visual)
orderBlockBorderStyle   = orderBlockBorderStyleOpt == "solid" ? line.style_solid : orderBlockBorderStyleOpt == "dashed" ? line.style_dashed : line.style_dotted
orderBlockBorderWidth   = input.int(1, "Border Width", minval=1, group=group_visual)

// Advanced Visual Effects
applyBlockAgeFading     = input.bool(true, "⏰ Age Fading", group=group_visual, tooltip="Older blocks become more transparent")
pulseNewBlocks = input.bool(true, "💓 Pulse New Blocks", group=group_visual, tooltip="Subtle pulsing effect for newly created blocks")
highlightActiveBlocks = input.bool(true, "🎯 Highlight Active", group=group_visual, tooltip="Special highlighting for blocks being tested")
showTrendArrows = input.bool(true, "➡️ Trend Arrows", group=group_visual, tooltip="Directional indicators on blocks")

// Layout and Positioning
labelOffset             = input.int(5, "Label Offset", group=group_visual, tooltip="Vertical offset for price labels to reduce overlap")
smartSpacing = input.bool(true, "📏 Smart Spacing", group=group_visual, tooltip="Automatic spacing adjustment based on chart zoom")
midlineLineWidth        = input.int(1, "Midline Width", minval=1, group=group_visual)
tableTextSizeOpt        = input.string("small", "Table Text Size", options=["tiny", "small", "normal", "large"], group=group_visual, tooltip="Text size for all tables")
tableTextSize           = tableTextSizeOpt == "tiny" ? size.tiny : tableTextSizeOpt == "small" ? size.small : tableTextSizeOpt == "normal" ? size.normal : size.large

// Interactive Features
showPerformanceMetrics = input.bool(true, "📊 Performance Panel", group=group_visual, tooltip="Live performance tracking panel")
showMarketRegimeIndicator = input.bool(true, "🌡️ Market Regime", group=group_visual, tooltip="Visual market condition indicator")
showPerformancePanel = input.bool(true, "📊 Performance Panel", group=group_visual, tooltip="Show performance statistics")
showMarketRegime = input.bool(true, "📈 Market Regime", group=group_visual, tooltip="Display market regime indicator")

// ----- Smart Filtering -----
group_smart = "🧠 Smart Filtering"
useSmartFiltering = input.bool(true, "Enable Smart Filtering", group=group_smart, tooltip="Filter out low-quality zones based on multiple criteria")
showZoneStrength = input.bool(true, "Show Zone Strength", group=group_smart, tooltip="Display strength rating for each zone")
minZoneStrength = input.float(1.2, "Min Zone Strength", minval=0.5, step=0.1, group=group_smart, tooltip="Minimum strength multiplier for a zone to be displayed")
useVolumeProfile = input.bool(true, "Use Volume Profile", group=group_smart, tooltip="Consider volume profile when evaluating zone strength")
minVolumeRatio = input.float(1.5, "Min Volume Ratio", minval=1.0, step=0.1, group=group_smart, tooltip="Minimum ratio of zone volume to average volume")
dynamicStarRating = input.bool(true, "Dynamic Star Rating", group=group_smart, tooltip="Adjust star rating threshold based on market volatility")
volatilityAdjustment = input.float(0.8, "Volatility Adjustment", minval=0.1, maxval=2.0, step=0.1, group=group_smart, tooltip="Strength of volatility adjustment (higher values = stronger adjustment)")

// Weighted Confluence Settings
// Removed weight variables - using original sr old.pine scoring system

// ----- Confluence Detection -----
group_confluence = "🎯 Confluence Detection"
detectConfluence = input.bool(true, "Detect Confluence", group=group_confluence, tooltip="Highlight zones that align with other technical factors")
highlightConfluence = input.bool(true, "Highlight Confluence Zones", group=group_confluence, tooltip="Apply special highlighting to zones with confluence")
prioritizeConfluence = input.bool(true, "Prioritize Confluence", group=group_confluence, tooltip="Prioritize blocks with confluence factors when filtering")
confluenceBoost = input.float(1.5, "Confluence Boost", minval=1.0, maxval=3.0, step=0.1, group=group_confluence, tooltip="Boost strength score for blocks with confluence")
useRSI = input.bool(true, "Use RSI", group=group_confluence, tooltip="Consider RSI levels for confluence")
rsiPeriod = input.int(14, "RSI Period", minval=1, group=group_confluence)
rsiOverbought = input.int(70, "RSI Overbought", minval=50, maxval=100, group=group_confluence)
rsiOversold = input.int(30, "RSI Oversold", minval=0, maxval=50, group=group_confluence)
useMACD = input.bool(true, "Use MACD", group=group_confluence, tooltip="Consider MACD for confluence")
macdFastLength = input.int(12, "MACD Fast Length", minval=1, group=group_confluence)
macdSlowLength = input.int(26, "MACD Slow Length", minval=1, group=group_confluence)
macdSignalLength = input.int(9, "MACD Signal Length", minval=1, group=group_confluence)

// ----- Time Filtering -----
group_time_filter    = "⏰ Time Filtering"
enableTimeFilter     = input.bool(false, "Enable Time Filtering", group=group_time_filter, tooltip="Filter out older blocks based on age")
maxBlockAge          = input.int(100, "Max Block Age (bars)", minval=10, maxval=500, group=group_time_filter, tooltip="Maximum age in bars for blocks to be displayed")

// ----- Reversal Detection -----
group_reversal = "🔄 Reversal Detection"
showReversalTriangles = input.bool(true, "🔼🔽 Show Reversals", group=group_reversal)
bullishReversalColor  = input.color(color.rgb(255,80,0,60), "🟢 Bullish", group=group_reversal)
bearishReversalColor  = input.color(color.rgb(0,255,128,60), "🔴 Bearish", group=group_reversal)

// ----- Moving Averages -----
group_ma    = "📈 Moving Averages"
show_ema200    = input.bool(false, "� EMA 200", group=group_ma, tooltip="200-period Exponential Moving Average")
ema200_source  = input.source(close, "EMA200 Source", group=group_ma)
ema200_length  = input.int(200, "EMA200 Length", minval=1, group=group_ma)
ema200_color   = input.color(color.rgb(0,128,255,80), "EMA200 Color", group=group_ma)

show_ema50   = input.bool(false, "🟣 EMA 50", group=group_ma, tooltip="50-period Exponential Moving Average")
ema50_source = input.source(close, "EMA50 Source", group=group_ma)
ema50_length = input.int(50, "EMA50 Length", minval=1, group=group_ma)
ema50_color  = input.color(color.rgb(178,102,255,80), "EMA50 Color", group=group_ma)

show_sma    = input.bool(false, "� SMA", group=group_ma, tooltip="Simple Moving Average")
sma_source  = input.source(high, "SMA Source", group=group_ma)
sma_length  = input.int(25, "SMA Length", minval=1, group=group_ma)
sma_color   = input.color(color.rgb(255,215,0,80), "SMA Color", group=group_ma)

// ====================================================================
// UTILITY FUNCTIONS
// ====================================================================

// Cleanup function to remove all visual elements of an order block
// This reduces code duplication and improves maintainability
f_cleanupOrderBlock(ob) =>
    if not na(ob)
        box.delete(ob.box)
        if not na(ob.midline)
            line.delete(ob.midline)
        if not na(ob.top_price_label)
            label.delete(ob.top_price_label)
        if not na(ob.bottom_price_label)
            label.delete(ob.bottom_price_label)
        if not na(ob.confluence_label)
            label.delete(ob.confluence_label)

// ====================================================================
// FUNCTIONS FOR MARKET REGIME DETECTION
// ====================================================================

// Improved ADX calculation that uses the built-in function correctly
f_adx(length) =>
    // Get ADX directly from the third return value of ta.dmi
    [_, _, adx] = ta.dmi(length, length)
    adx

// Detect market regime (trending, ranging, or volatile)
// Returns: 0 = ranging, 1 = trending, 2 = volatile
f_detectMarketRegime() =>
    // Calculate ADX for trend strength
    float adx_value = f_adx(14)

    // Calculate volatility with zero-division protection
    float volatility = close != 0 ? (ta.atr(dynamicATRPeriod) / close * 100) : 0

    // Calculate price movement consistency
    int up_bars = 0
    int down_bars = 0
    for i = 0 to 9
        if close[i] > close[i+1]
            up_bars += 1
        else if close[i] < close[i+1]
            down_bars += 1

    float consistency = math.abs(up_bars - down_bars) / 10

    // Determine regime
    if adx_value > 30 and consistency > 0.6
        1  // Trending
    else if volatility > 2.5  // High volatility threshold (2.5% of price)
        2  // Volatile
    else
        0  // Ranging

// Get timeframe multiplier for lookback adaptation
f_getTimeframeMultiplier() =>
    float tf_minutes = timeframe.in_seconds() / 60

    if tf_minutes <= 5
        1.0  // Lower timeframes - use standard lookback
    else if tf_minutes <= 15
        1.2  // 15m
    else if tf_minutes <= 60
        1.5  // 1h
    else if tf_minutes <= 240
        2.0  // 4h
    else if tf_minutes <= 1440
        3.0  // Daily
    else
        4.0  // Weekly and above

// Global variables for caching
var int   cachedRegime = na
var int   lastRegimeUpdate = na
var float cachedATR = na

// Enhanced adaptive box size function
f_getAdaptiveBoxSize() =>
    // Calculate ATR internally
    float current_atr = ta.atr(dynamicATRPeriod)
    
    // Base calculation using the calculated ATR
    float base_size = current_atr * dynamicBoxMultiplier
    float size_mult = 1.0  // Default multiplier (neutral)

    // Extract function call for consistency
    int box_regime = f_detectMarketRegime()

    // Adjust based on regime
    if box_regime == 1  // Trending
        // Wider boxes in trending markets
        size_mult := 1.3
    else if box_regime == 2  // Volatile
        // Wider boxes in volatile markets
        size_mult := 1.5
    else  // Ranging
        // Narrower boxes in ranging markets
        size_mult := 0.8

    // Apply volatility weight with safety checks using the calculated ATR
    float atr_sma = ta.sma(current_atr, 50)
    float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0
    float volatility_adjustment = 1.0 + math.max(-0.5, math.min(0.5, volatility_factor - 1.0)) * boxSizeVolatilityWeight

    // Calculate final size with bounds using the calculated ATR
    float final_size = base_size * size_mult * volatility_adjustment
    math.max(minBoxSize * current_atr, math.min(maxBoxSize * current_atr, final_size))

// ====================================================================
// ENHANCED VOLUME ANALYSIS FUNCTIONS
// ====================================================================

f_detectVolumeSpike(pivot_bar_offset) =>
    if not checkVolumeSpike
        true // Skip check if disabled
    else
        avg_volume = ta.sma(volume, 20)[pivot_bar_offset]
        
        bool spike_detected = false
        // Check the window starting from the pivot bar and looking towards the present
        for i = 0 to volumeSpikeWindow - 1
            // Ensure we don't look into the future
            int check_index = pivot_bar_offset - i
            if check_index >= 0
                if volume[check_index] >= avg_volume * volumeSpikeFactor
                    spike_detected := true
                    break
        
        spike_detected

// Enhanced volume profile analysis within block range
f_analyzeVolumeProfile(upper_level, lower_level, lookback_bars) =>
    float total_volume_in_range = 0.0
    float volume_near_edges = 0.0
    int bars_in_range = 0
    
    // Analyze volume distribution within the price range
    for i = 0 to math.min(lookback_bars, 50)  // Limit to prevent excessive computation
        float bar_high = high[i]
        float bar_low = low[i]
        float bar_volume = volume[i]
        
        // Check if bar intersects with our range
        if bar_high >= lower_level and bar_low <= upper_level
            total_volume_in_range += bar_volume
            bars_in_range += 1
            
            // Check if volume is concentrated near edges (top/bottom 25% of range)
            float range_size = upper_level - lower_level
            float edge_threshold = range_size * 0.25
            
            if (bar_high >= upper_level - edge_threshold) or (bar_low <= lower_level + edge_threshold)
                volume_near_edges += bar_volume
    
    // Calculate edge concentration ratio
    float edge_concentration = total_volume_in_range > 0 ? volume_near_edges / total_volume_in_range : 0.0
    
    [total_volume_in_range, edge_concentration, bars_in_range]

// ====================================================================
// FUNCTIONS FOR CONFLUENCE DETECTION AND ZONE STRENGTH
// ====================================================================

// Calculate RSI confluence
f_rsiConfluence(level, is_support) =>
    rsi_value = ta.rsi(close, rsiPeriod)
    if is_support
        rsi_value <= rsiOversold  // Oversold condition for support
    else
        rsi_value >= rsiOverbought  // Overbought condition for resistance

// Calculate MACD confluence
f_macdConfluence(level, is_support) =>
    [macdLine, signalLine, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
    if is_support
        macdLine < 0 and macdLine > signalLine  // Bullish crossover below zero
    else
        macdLine > 0 and macdLine < signalLine  // Bearish crossover above zero

// Calculate MA confluence
f_maConfluence(level, is_support) =>
    // Dynamic tolerance: larger of 1% or 0.5 ATR
    float tolPerc = level * 0.01
    float tolAtr = ta.atr(dynamicATRPeriod) * 0.5
    float tol = math.max(tolPerc, tolAtr)

    float ema50_val = ta.ema(ema50_source, ema50_length)
    float ema200_val = ta.ema(ema200_source, ema200_length)

    bool near_ema50 = math.abs(level - ema50_val) <= tol
    bool near_ema200 = math.abs(level - ema200_val) <= tol

    near_ema50 or near_ema200

// Calculate volume confluence
f_volumeConfluence(volume) =>
    avgVolume = ta.sma(volume, 20)
    volume >= avgVolume * minVolumeRatio

// f_calculateZoneStrength() - Original sr old.pine implementation for better accuracy
// Simplified scoring system that provides more reliable strength ratings
f_calculateZoneStrength(float vol_ratio, bool rsi_conf, bool macd_conf, bool ma_conf, bool vol_conf) =>
    // Base strength from volume
    float volumeStrength = math.min(vol_ratio / minVolumeRatio, 2.0) * 2.5  // 0-5 points from volume

    // Confluence bonus (original sr old.pine values)
    float confluenceBonus = 0.0
    int confluenceCount = 0

    if rsi_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if macd_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if ma_conf
        confluenceBonus += 1.5
        confluenceCount += 1
    if vol_conf
        confluenceBonus += 1.5
        confluenceCount += 1

    // Apply confluence boost if enabled and block has confluence
    if prioritizeConfluence and confluenceCount > 0
        float boost_factor = 1.0 + (confluenceCount / 4.0) * (confluenceBoost - 1.0)
        confluenceBonus *= boost_factor

    // Calculate total score (0-10 scale)
    float totalScore = math.min(volumeStrength + confluenceBonus, 10.0)
    totalScore

// Get color-coded strength rating text
// Original sr old.pine thresholds for consistent rating
f_getColorCodedStarRating(strength) =>
    if strength >= 8.0
        "🟢🟢🟢🟢🟢"  // Green for strongest
    else if strength >= 6.0
        "🟢🟢🟢🟢⚪"
    else if strength >= 4.0
        "🟡🟡🟡⚪⚪"  // Yellow for medium
    else if strength >= 2.0
        "🟠🟠⚪⚪⚪"  // Orange for weaker
    else
        "🔴⚪⚪⚪⚪"  // Red for weakest

// Cache star rating thresholds for better performance
// Original sr old.pine thresholds
var float[] base_star_thresholds = array.new_float(5, 0.0)
if barstate.isfirst
    array.set(base_star_thresholds, 0, 0.0)  // 1 star
    array.set(base_star_thresholds, 1, 2.0)  // 2 stars
    array.set(base_star_thresholds, 2, 4.0)  // 3 stars
    array.set(base_star_thresholds, 3, 6.0)  // 4 stars
    array.set(base_star_thresholds, 4, 8.0)  // 5 stars

// Get star rating threshold based on minimum star rating input
// Optimized with cached thresholds and reduced calculations
f_getStarRatingThreshold(star_rating) =>
    // Get base threshold from cache (with bounds checking)
    int index = math.max(0, math.min(4, star_rating - 1))
    float threshold = array.get(base_star_thresholds, index)

    // Apply dynamic adjustment based on market volatility if enabled
    if dynamicStarRating and star_rating > 1
        // Calculate current volatility relative to recent history
        float current_atr = ta.atr(dynamicATRPeriod)
        float atr_sma = ta.sma(current_atr, 50)
        float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0

        // Determine market regime for additional context
        int market_regime = f_detectMarketRegime()

        // Adjust threshold based on volatility and market regime
        float adjustment_factor = 1.0

        if market_regime == 2  // Volatile market
            // Lower threshold in volatile markets to show more blocks
            adjustment_factor := math.max(0.7, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment)
        else if market_regime == 1  // Trending market
            // Slightly lower threshold in trending markets
            adjustment_factor := math.max(0.85, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment * 0.5)

        threshold := threshold * adjustment_factor

    threshold

// ====================================================================
// THEME AND VISUAL ENHANCEMENT FUNCTIONS
// ====================================================================

// Get theme-based colors
f_getThemeColors(string theme, bool is_support) =>
    color fill_color = color.white
    color border_color = color.white
    
    if theme == "Professional"
        fill_color := is_support ? color.rgb(0, 150, 100, 80) : color.rgb(200, 50, 50, 80)
        border_color := is_support ? color.rgb(0, 200, 120) : color.rgb(255, 80, 80)
    else if theme == "Dark"
        fill_color := is_support ? color.rgb(0, 255, 150, 85) : color.rgb(255, 100, 100, 85)
        border_color := is_support ? color.rgb(0, 255, 200) : color.rgb(255, 150, 150)
    else if theme == "Neon"
        fill_color := is_support ? color.rgb(0, 255, 255, 70) : color.rgb(255, 0, 255, 70)
        border_color := is_support ? color.rgb(0, 255, 255) : color.rgb(255, 0, 255)
    else if theme == "Minimal"
        fill_color := is_support ? color.rgb(100, 100, 100, 90) : color.rgb(150, 150, 150, 90)
        border_color := is_support ? color.rgb(120, 120, 120) : color.rgb(180, 180, 180)
    else if theme == "Colorblind"
        fill_color := is_support ? color.rgb(0, 100, 200, 80) : color.rgb(200, 100, 0, 80)
        border_color := is_support ? color.rgb(0, 150, 255) : color.rgb(255, 150, 0)
    else // Custom - use user-defined colors
        fill_color := is_support ? bullColor : bearColor
        border_color := is_support ? bullBorderColor : bearBorderColor
    
    [fill_color, border_color]

// Apply gradient effect to colors
f_applyGradientEffect(color base_color, float intensity, bool is_top) =>
    if blockStyle == "Gradient" and gradientIntensity > 0
        int transparency_adjust = int(intensity * 30)
        if is_top
            color.new(base_color, color.t(base_color) + transparency_adjust)
        else
            color.new(base_color, math.max(0, color.t(base_color) - transparency_adjust))
    else
        base_color

// Apply glow effect
f_applyGlowEffect(color base_color, bool has_glow) =>
    if glowEffect and has_glow
        color.new(base_color, math.max(0, color.t(base_color) - 20))
    else
        base_color

// Get smart label position to avoid overlaps
f_getSmartLabelPosition(float price_level, int bar_idx, bool is_support) =>
    float offset_multiplier = smartLabels ? 1.5 : 1.0
    float base_offset = labelOffset * offset_multiplier
    
    // Adjust based on chart zoom and spacing
    if smartSpacing
        float zoom_factor = math.log(ta.atr(20) / ta.atr(200)) + 1
        base_offset := base_offset * math.max(0.5, math.min(2.0, zoom_factor))
    
    is_support ? price_level - base_offset : price_level + base_offset

// Get icon for label based on block type and state
f_getBlockIcon(bool is_support, bool is_broken, bool is_mitigated, bool has_confluence) =>
    if not iconLabels
        is_support ? "SUP" : "RES"
    else if is_mitigated
        "⚡"
    else if is_broken
        "💥"
    else if has_confluence
        "🎯"
    else if is_support
        "🔺"
    else
        "🔻"

// Calculate pulse effect for new blocks
f_getPulseEffect(int created_bar, int current_bar) =>
    if pulseNewBlocks and (current_bar - created_bar) < 10
        float pulse_factor = math.sin((current_bar - created_bar) * 0.5) * 0.3 + 0.7
        math.max(0.4, pulse_factor)
    else
        1.0

// ====================================================================
// ENHANCED COLOR MANAGEMENT FUNCTIONS
// ====================================================================

// Calculate enhanced colors based on strength, volume, and state
f_getEnhancedColors(bool is_support, float strength, float vol_ratio, bool has_confluence, bool is_broken, bool is_mitigated) =>
    // Get theme-based base colors
    [theme_fill, theme_border] = f_getThemeColors(themePreset, is_support)
    
    // Base colors (use theme or custom)
    color base_fill = themePreset == "Custom" ? (is_support ? bullColor : bearColor) : theme_fill
    color base_border = themePreset == "Custom" ? (is_support ? bullBorderColor : bearBorderColor) : theme_border
    
    // State-based color adjustments
    if is_mitigated
        base_fill := mitigatedColor
        base_border := mitigatedBorderColor
    else if is_broken
        base_fill := breakoutColor
        base_border := brokenBorderColor
    
    // Apply enhanced visual effects if enabled
    if enhancedColorMode
        // Strength-based color intensity
        if strengthBasedColors and not is_mitigated and not is_broken
            float strength_factor = math.min(strength / 10.0, 1.0)
            base_transparency = color.t(base_fill)
            adjusted_transparency = int(base_transparency * (1.0 - strength_factor * 0.3))
            base_fill := color.new(color.rgb(color.r(base_fill), color.g(base_fill), color.b(base_fill)), adjusted_transparency)
        
        // Apply gradient effect
        if blockStyle == "Gradient"
            base_fill := f_applyGradientEffect(base_fill, gradientIntensity, false)
        
        // Apply glow effect for confluence or high-strength blocks
        if glowEffect and (has_confluence or strength > 7.0)
            base_fill := f_applyGlowEffect(base_fill, true)
            base_border := f_applyGlowEffect(base_border, true)
    
    [base_fill, base_border]

// Calculate border width based on volume ratio
f_getBorderWidth(float vol_ratio, bool has_confluence) =>
    int base_width = orderBlockBorderWidth
    
    if volumeBasedBorders and vol_ratio > 0
        // Reduced border width increases for high-volume zones
        if vol_ratio >= 4.0
            base_width := base_width + 1
        else if vol_ratio >= 2.5
            base_width := base_width + 0
    
    // Reduced extra width for confluence zones
    if has_confluence
        base_width := base_width + 0
    
    math.min(base_width, 3)  // Reduced cap to 3 pixels

// ====================================================================
// TYPE DEFINITIONS
// ====================================================================

// Enhanced OrderBlock type with additional properties
type OrderBlock
    // Core block properties
    box box
    float upper_level
    float lower_level
    bool is_support
    int left_index
    int created_at

    // Volume information
    float total_volume
    float buy_volume
    float sell_volume

    // State tracking
    bool is_broken
    bool is_mitigated
    bool is_display_hidden  // New flag to track blocks hidden for display but kept for analysis

    // Strength and quality metrics
    float strength_score
    float volume_ratio
    bool has_confluence
    int retest_count
    float success_rate
    bool strength_calculated  // Flag to track if strength has been calculated
    bool meets_min_rating     // Flag to track if block meets minimum rating

    // Confluence factors
    bool rsi_confluence
    bool macd_confluence
    bool ma_confluence
    bool volume_confluence

    // Visual elements
    line midline
    label top_price_label
    label bottom_price_label
    label confluence_label

    // Performance tracking
    bool was_successful
    int last_touched_bar
    bool success_evaluated

// Type to store lookback test results
type LookbackTestResult
    int lookback_value
    float total_strength
    int block_count

// ====================================================================
// UTILITY FUNCTIONS (MUST BE DEFINED BEFORE USE)
// ====================================================================

// Faster up/down volume calculation with single comparison
upAndDownVolume() =>
    close > open ? volume : -volume

// Enhanced pivot calculation with volume spike validation
calcPivots(src, lb) =>
    // Ensure lookback is valid and reasonable
    validLb = math.max(1, math.min(50, lb))  // Cap at 50 to prevent extreme values

    // Use try/catch approach for pivot calculations
    float pivotHigh = na
    float pivotLow = na

    // Calculate pivots with error handling
    if not na(src) and validLb > 0 and validLb < 100  // Additional safety check
        float temp_pivotHigh = ta.pivothigh(src, validLb, validLb)
        float temp_pivotLow = ta.pivotlow(src, validLb, validLb)

        // Validate pivots with volume spike detection if enabled
        if not na(temp_pivotHigh)
            // Pass the offset of the pivot bar
            if f_detectVolumeSpike(validLb)
                pivotHigh := temp_pivotHigh

        if not na(temp_pivotLow)
            // Pass the offset of the pivot bar
            if f_detectVolumeSpike(validLb)
                pivotLow := temp_pivotLow

    [pivotHigh, pivotLow]

// Improved support and resistance calculation with caching and error handling
calcSupportResistance(src, lb, Vol, vol_sma, current_atr) =>
    // Cache volume thresholds for efficiency
    float volThreshold = vol_sma * volMultiplier

    // Get pivot points (only calculate once)
    [pivotHigh, pivotLow] = calcPivots(src, lb)

    // Calculate box width with enhanced adaptivity
    float width = 0.0
    if useDynamicBox
        if adaptiveBoxSize
            // Use the advanced adaptive box sizing
            width := f_getAdaptiveBoxSize() // ATR will be calculated internally or use global
        else
            // Use the standard dynamic box sizing
            width := current_atr * dynamicBoxMultiplier
    else
        width := current_atr * box_width

    // Initialize variables with default values
    float supportLevel = na
    float supportLevelAlt = na
    float resistanceLevel = na
    float resistanceLevelAlt = na
    color sup_color = bullColor
    color res_color = bearColor
    bool breakout_res = false
    bool res_holds = false
    bool sup_holds = false
    bool breakout_sup = false

    // Volume requirement check
    bool volumeSignificant = nz(Vol, 0) > volThreshold

    // Set support levels if conditions met
    if not na(pivotLow) and volumeSignificant
        supportLevel := pivotLow
        supportLevelAlt := pivotLow - width

    // Set resistance levels if conditions met
    if not na(pivotHigh) and volumeSignificant
        resistanceLevel := pivotHigh
        resistanceLevelAlt := pivotHigh + width

    // Calculate breakouts and holds only if levels are valid
    if not na(resistanceLevel)
        breakout_res := ta.crossover(high, resistanceLevelAlt) //low
        res_holds := ta.crossunder(high, resistanceLevel)

    if not na(supportLevel)
        breakout_sup := ta.crossunder(low, supportLevel) //crossover low
        sup_holds := ta.crossunder(high, supportLevelAlt)

    // Return all values in a consistent order
    [supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt,
     sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol]

// ====================================================================
// FUNCTIONS FOR LOOKBACK OPTIMIZATION
// ====================================================================

// Test a specific lookback period and return strength metrics
f_testLookbackPeriod(test_lookback) =>
    // Validate lookback period
    int valid_lookback = math.max(2, math.min(50, test_lookback))

    float total_strength = 0.0
    int valid_blocks = 0

    // Calculate volume SMA and ATR for this test
    float test_volSMA = ta.sma(math.abs(upAndDownVolume()), vol_len)
    float test_atr = ta.atr(dynamicATRPeriod)

    // Calculate support and resistance levels using test lookback
    [test_supportLevel, test_supportLevelAlt, test_resistanceLevel, test_resistanceLevelAlt,
     test_sup_color, test_res_color, test_breakout_res, test_res_holds, test_sup_holds,
     test_breakout_sup, test_Vol] = calcSupportResistance(close, valid_lookback, math.abs(upAndDownVolume()), test_volSMA, test_atr)

    // Test support block creation
    if not na(test_supportLevel) and test_Vol > test_volSMA * volMultiplier and valid_blocks < maxBlocksPerLookback
        // Calculate volume metrics for test lookback
        float test_buyVol = 0.0
        float test_sellVol = 0.0
        float test_totalVol = 0.0
        for i = 1 to valid_lookback
            test_totalVol += volume[i]
            if close[i] > open[i]
                test_buyVol += volume[i]
            else if close[i] < open[i]
                test_sellVol += volume[i]

        // Calculate strength for this test block
        float test_vol_ratio = test_totalVol / math.max(1.0, ta.sma(volume, 20))

        // Confluence calculations (frozen at test pivot bar)
        float test_rsi_val = ta.rsi(close, rsiPeriod)[valid_lookback]
        [test_macdL, test_macdS, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
        float test_macdL_pivot = test_macdL[valid_lookback]
        float test_macdS_pivot = test_macdS[valid_lookback]

        bool test_rsi_conf = useRSI and detectConfluence and test_rsi_val <= rsiOversold
        bool test_macd_conf = useMACD and detectConfluence and (test_macdL_pivot < 0 and test_macdL_pivot > test_macdS_pivot)
        bool test_ma_conf = detectConfluence ? f_maConfluence(test_supportLevel, true) : false
        bool test_vol_conf = useVolumeProfile ? f_volumeConfluence(test_totalVol) : false

        float test_strength = f_calculateZoneStrength(test_vol_ratio, test_rsi_conf, test_macd_conf, test_ma_conf, test_vol_conf)

        // Apply minimum filters
        float test_star_thresh = f_getStarRatingThreshold(min_star_rating)
        bool test_smart_filter_pass = not useSmartFiltering or test_vol_ratio >= minVolumeRatio or (test_rsi_conf or test_macd_conf or test_ma_conf or test_vol_conf)
        bool test_star_filter_pass = test_strength >= test_star_thresh

        if test_smart_filter_pass and test_star_filter_pass
            total_strength += test_strength
            valid_blocks += 1

    // Test resistance block creation
    if not na(test_resistanceLevel) and test_Vol > test_volSMA * volMultiplier and valid_blocks < maxBlocksPerLookback
        // Calculate volume metrics for test lookback
        float test_buyVol = 0.0
        float test_sellVol = 0.0
        float test_totalVol = 0.0
        for i = 1 to valid_lookback
            test_totalVol += volume[i]
            if close[i] > open[i]
                test_buyVol += volume[i]
            else if close[i] < open[i]
                test_sellVol += volume[i]

        // Calculate strength for this test block
        float test_vol_ratio = test_totalVol / math.max(1.0, ta.sma(volume, 20))

        // Confluence calculations (frozen at test pivot bar)
        float test_rsi_val = ta.rsi(close, rsiPeriod)[valid_lookback]
        [test_macdL, test_macdS, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
        float test_macdL_pivot = test_macdL[valid_lookback]
        float test_macdS_pivot = test_macdS[valid_lookback]

        bool test_rsi_conf = useRSI and detectConfluence and test_rsi_val >= rsiOverbought
        bool test_macd_conf = useMACD and detectConfluence and (test_macdL_pivot > 0 and test_macdL_pivot < test_macdS_pivot)
        bool test_ma_conf = detectConfluence ? f_maConfluence(test_resistanceLevel, false) : false
        bool test_vol_conf = useVolumeProfile ? f_volumeConfluence(test_totalVol) : false

        float test_strength = f_calculateZoneStrength(test_vol_ratio, test_rsi_conf, test_macd_conf, test_ma_conf, test_vol_conf)

        // Apply minimum filters
        float test_star_thresh = f_getStarRatingThreshold(min_star_rating)
        bool test_smart_filter_pass = not useSmartFiltering or test_vol_ratio >= minVolumeRatio or (test_rsi_conf or test_macd_conf or test_ma_conf or test_vol_conf)
        bool test_star_filter_pass = test_strength >= test_star_thresh

        if test_smart_filter_pass and test_star_filter_pass
            total_strength += test_strength
            valid_blocks += 1

    // Return test results
    LookbackTestResult.new(valid_lookback, total_strength, valid_blocks)

// Find optimal lookback period by testing multiple values
f_optimizeLookbackPeriod() =>
    if not enableLookbackOptimization
        lookbackPeriod  // Return original lookback if optimization disabled
    else
        // Validate range inputs
        int min_range = math.max(2, math.min(maxLookbackRange - 1, minLookbackRange))
        int max_range = math.max(min_range + 1, math.min(50, maxLookbackRange))

        // Variables to track best result
        float best_strength = 0.0
        int best_lookback = lookbackPeriod
        int best_block_count = 0

        // Test each lookback period in the range
        for test_lb = min_range to max_range
            LookbackTestResult test_result = f_testLookbackPeriod(test_lb)

            // Calculate weighted score (strength per block to normalize for different block counts)
            float weighted_score = test_result.block_count > 0 ? test_result.total_strength / test_result.block_count : 0.0

            // Update best result if this test is better
            // Prioritize higher total strength, but also consider block count
            bool is_better = false
            if test_result.total_strength > best_strength
                is_better := true
            else if test_result.total_strength == best_strength and test_result.block_count > best_block_count
                is_better := true

            if is_better
                best_strength := test_result.total_strength
                best_lookback := test_result.lookback_value
                best_block_count := test_result.block_count

        // Debug: Show optimization results if debug mode is enabled
        if debugMode
            string debug_text = "🎯 Optimization Results:\n" +
                               "Range: " + str.tostring(min_range) + "-" + str.tostring(max_range) + "\n" +
                               "Best: " + str.tostring(best_lookback) + " (Strength: " + str.tostring(math.round(best_strength, 2)) + ", Blocks: " + str.tostring(best_block_count) + ")\n" +
                               "Manual: " + str.tostring(lookbackPeriod)
            label.new(bar_index, low, debug_text,
                     color=color.new(color.green, 20), style=label.style_label_up, textcolor=color.white, size=size.small)

        best_lookback

// ====================================================================
// FUNCTIONS FOR AUTOMATED SENSITIVITY OPTIMIZATION
// ====================================================================
// Optimized sum function using built-in math.sum
f_sum(src, len) =>
    math.sum(src, len)

// --- Enhanced Global Variables ---

// Constants for success criteria
var int SUCCESS_BARS_REQUIRED = 20  // Number of bars an orderblock must remain intact after being touched to be considered successful

// Cache ATR calculation with proper initialization
var float atr = ta.atr(dynamicATRPeriod)
atr := nz(ta.atr(dynamicATRPeriod), atr) // Update with new value or keep previous if NA

// Lookback optimization variables
var int optimizedLookback = na
var int lastOptimizationBar = 0
var bool optimizationRan = false

// Initialize optimizedLookback only once
if na(optimizedLookback)
    optimizedLookback := lookbackPeriod

// Calculate optimized lookback period (update every 10 bars to balance performance, but run more frequently initially)
bool shouldOptimize = enableLookbackOptimization and
                     ((bar_index < 50 and (bar_index - lastOptimizationBar) >= 5) or  // More frequent optimization in first 50 bars
                      (bar_index - lastOptimizationBar) >= 10)  // Normal frequency after 50 bars

if shouldOptimize
    int newOptimizedLookback = f_optimizeLookbackPeriod()
    if debugMode
        label.new(bar_index, low, "🔍 Testing Optimization: Manual=" + str.tostring(lookbackPeriod) + ", Current=" + str.tostring(optimizedLookback) + ", New=" + str.tostring(newOptimizedLookback),
                 color=color.new(color.purple, 20), style=label.style_label_up, textcolor=color.white, size=size.small)

    if newOptimizedLookback != optimizedLookback
        optimizedLookback := newOptimizedLookback
        optimizationRan := true
        if debugMode
            label.new(bar_index, high, "🔍 Lookback Optimized: " + str.tostring(lookbackPeriod) + " → " + str.tostring(optimizedLookback),
                     color=color.new(color.blue, 20), style=label.style_label_down, textcolor=color.white, size=size.small)
    lastOptimizationBar := bar_index

// Use optimized or manual lookback period
int effectiveLookback = enableLookbackOptimization ? optimizedLookback : lookbackPeriod

// Initialize order blocks array
var order_blocks = array.new<OrderBlock>()
// Track filtered blocks count (needs to be updated in main code, not in functions)
var int filtered_blocks_count = 0
// Arrays to track filtered blocks from functions
var filtered_by_create = array.new<bool>(0)
var filtered_by_update = array.new<bool>(0)

// Efficient volume tracking
var float volSMA = 0.0

// Enhanced volume formatting with string caching for better performance
// Cache for formatted volume strings to avoid redundant calculations
var int MAX_CACHE_SIZE = 20  // Limit cache size to prevent memory issues
var float[] cached_volume_values = array.new_float(0)
var string[] cached_volume_strings = array.new_string(0)

// Initialize cache arrays on first bar to ensure they're properly set up
if barstate.isfirst
    array.clear(cached_volume_values)
    array.clear(cached_volume_strings)

formatVolume(vol) =>
    float vol_val = nz(vol, 0)
    
    // Handle invalid volumes safely
    if vol_val <= 0
        "0"
    else
        string formatted_vol = ""
        int cache_index = -1
        
        // Search cache only if not empty
        if array.size(cached_volume_values) > 0
            for i = 0 to array.size(cached_volume_values) - 1
                // Allow 0.1% tolerance for floating point comparison
                if math.abs(array.get(cached_volume_values, i) - vol_val) < vol_val * 0.001
                    cache_index := i
                    break
        
        // Return cached value if found
        if cache_index >= 0 and cache_index < array.size(cached_volume_strings)
            formatted_vol := array.get(cached_volume_strings, cache_index)
        else
            // Format the volume
            if vol_val >= 1e9
                formatted_vol := str.format("{0}B", str.tostring(math.round(vol_val / 1e9, 2)))
            else if vol_val >= 1e6
                formatted_vol := str.format("{0}M", str.tostring(math.round(vol_val / 1e6, 2)))
            else if vol_val >= 1e3
                formatted_vol := str.format("{0}K", str.tostring(math.round(vol_val / 1e3, 2)))
            else
                formatted_vol := str.tostring(math.round(vol_val))
            
            // Add to cache with size management
            array.push(cached_volume_values, vol_val)
            array.push(cached_volume_strings, formatted_vol)
            
            // Maintain cache size
            if array.size(cached_volume_values) > MAX_CACHE_SIZE
                array.shift(cached_volume_values)
                array.shift(cached_volume_strings)
        
        formatted_vol

// Get buy/sell dominance icon
getBuySellIcon(buy_pct, sell_pct) =>
    string icon = ""
    if buy_pct >= buySellDominanceThreshold
        icon := "🔵" // Blue circle for buy dominance
    else if sell_pct >= buySellDominanceThreshold
        icon := "🔴" // Red circle for sell dominance
    else
        icon := "🟡" // Yellow circle for neutral
    icon

getBuySellColor(buy_pct, sell_pct) =>
    color color = na
    if buy_pct >= buySellDominanceThreshold
        color := color.blue
    else if sell_pct >= buySellDominanceThreshold
        color := color.red
    else
        color := color.yellow
    color

// Create an order block with enhanced features
createOrderBlock(is_support, level, level_alt, total_vol, buy_vol, sell_vol, blockColor) =>
    // Pre-filters - trend filter evaluated on the PIVOT BAR (freeze it)
    float ema200_at_pivot = ta.ema(ema200_source, ema200_length)[effectiveLookback]
    bool trend_filter_pass = not useTrendFilter or
         (is_support and level >= ema200_at_pivot) or
         (not is_support and level <= ema200_at_pivot)

    if trend_filter_pass
        // Confluence values frozen at pivot bar
        float rsi_val = ta.rsi(close, rsiPeriod)[effectiveLookback]
        [macdL, macdS, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
        float macdL_pivot = macdL[effectiveLookback]
        float macdS_pivot = macdS[effectiveLookback]

        bool rsi_conf = useRSI and detectConfluence and 
             (is_support ? rsi_val <= rsiOversold : rsi_val >= rsiOverbought)

        bool macd_conf = useMACD and detectConfluence and 
             (is_support ? (macdL_pivot < 0 and macdL_pivot > macdS_pivot) : 
                         (macdL_pivot > 0 and macdL_pivot < macdS_pivot))

        bool ma_conf = detectConfluence ? f_maConfluence(level, is_support) : false
        bool vol_conf = useVolumeProfile ? f_volumeConfluence(total_vol) : false
        bool has_conf = rsi_conf or macd_conf or ma_conf or vol_conf

        // Calculate strength ONCE and use for all filtering
        float vol_ratio = total_vol / math.max(1.0, ta.sma(volume, 20))
        float strength_score = f_calculateZoneStrength(vol_ratio, rsi_conf, macd_conf, ma_conf, vol_conf)
        float star_thresh = f_getStarRatingThreshold(min_star_rating)

        // Apply all filters
        bool smart_filter_pass = not useSmartFiltering or vol_ratio >= minVolumeRatio or has_conf
        bool star_filter_pass = strength_score >= star_thresh

        if smart_filter_pass and star_filter_pass
            // Remove old blocks if at capacity
            if array.size(order_blocks) >= analysis_blocks
                while array.size(order_blocks) >= analysis_blocks
                    oldest_ob = array.pop(order_blocks)
                    f_cleanupOrderBlock(oldest_ob)

            // Create visual objects
            upper_level = math.max(level, level_alt)
            lower_level = math.min(level, level_alt)
            left_index = bar_index - effectiveLookback
            mid_level = (upper_level + lower_level) / 2

            [enhanced_fill_color, enhanced_border_color] = f_getEnhancedColors(is_support, strength_score, vol_ratio, has_conf, false, false)
            enhanced_border_width = f_getBorderWidth(vol_ratio, has_conf)

            new_box = box.new(
                 left_index, upper_level,
                 bar_index + extendObs, lower_level,
                 border_color=enhanced_border_color,
                 border_width=enhanced_border_width,
                 bgcolor=enhanced_fill_color,
                 extend=extend.right
                 )

            midlineObj = showMidline ? line.new(
                 left_index, mid_level,
                 bar_index + extendObs, mid_level,
                 color=midlineColor,
                 width=midlineLineWidth,
                 style=line.style_dashed
                 ) : na

            // Create the OrderBlock and add to array
            array.unshift(order_blocks, OrderBlock.new(
                 new_box, upper_level, lower_level, is_support, left_index,
                 bar_index, total_vol, buy_vol, sell_vol,
                 false, false, false,  // is_broken, is_mitigated, is_display_hidden
                 strength_score, vol_ratio, has_conf,
                 0, 0.0,  // retest_count, success_rate
                 true, true,  // strength_calculated, meets_min_rating
                 rsi_conf, macd_conf, ma_conf, vol_conf,
                 midlineObj, na, na, na,  // visual elements
                 false, 0, false  // success tracking
                 ))
                 
// Update order blocks with enhanced features
updateOrderBlocks() =>
    if array.size(order_blocks) == 0
        na
    else
        total_volume = 0.0
        for ob in order_blocks
            total_volume += ob.total_volume

        to_remove = array.new_int(0)
        for i = 0 to array.size(order_blocks) - 1
            ob = array.get(order_blocks, i)

            // ====================================================================
            // BUG FIX: STEP 1 - Check for Removal FIRST
            // ====================================================================
            // This block checks if the OB should be removed based on its state from the PREVIOUS bar.
            // This prevents the block from vanishing on the same bar it breaks.
            
            // Combine all removal conditions into a single block for efficiency.
            should_remove = (ob.is_broken and not showBreakerBlocks) or 
                             (ob.is_mitigated and not showMitigated and not ob.is_display_hidden) or
                             (enableTimeFilter and (bar_index - ob.created_at) > maxBlockAge) or
                             (useSmartFiltering and ob.strength_score < minZoneStrength and not ob.has_confluence) or
                             (not ob.meets_min_rating)

            if should_remove
                array.push(to_remove, i)
                // If it's marked for removal, we don't need to do any more processing or visual updates for it.
                continue

            // ====================================================================
            // STEP 2 - Update Performance & State for blocks that are NOT being removed
            // ====================================================================
            
            // Check for retests and update performance metrics
            price_range = math.abs(ob.upper_level - ob.lower_level)
            is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25 and not ob.is_broken) or
                           (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25 and not ob.is_broken)

            if is_retesting
                ob.retest_count += 1
                ob.last_touched_bar := bar_index
                if ob.success_evaluated
                    ob.success_evaluated := false
                    ob.was_successful := false
                
                retest_success = ob.is_support ? (close > ob.upper_level) : (close < ob.lower_level)
                if ob.retest_count > 0
                    current_success = ob.success_rate * (ob.retest_count - 1)
                    new_success = current_success + (retest_success ? 1 : 0)
                    ob.success_rate := new_success / ob.retest_count

            if not ob.success_evaluated and ob.last_touched_bar > 0 and (bar_index - ob.last_touched_bar) >= SUCCESS_BARS_REQUIRED
                ob.was_successful := true
                ob.success_evaluated := true
                if debugMode
                    label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Success: Survived " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars", color=color.rgb(0,255,0), style=label.style_label_down)

            // Update status using the current bar's close for real-time feedback
            if not ob.is_broken
                if (ob.is_support and close < ob.lower_level) or (not ob.is_support and close > ob.upper_level)
                    ob.is_broken := true
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Failed: Broken after " + str.tostring(bar_index - ob.last_touched_bar) + " bars", color=color.rgb(255,64,64), style=label.style_label_down)
                    else if debugMode
                        label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Broken: " + (ob.is_support ? "Support" : "Resistance"), color=color.rgb(255,64,64), style=label.style_label_down)

            if ob.is_broken and not ob.is_mitigated
                mitigation_level = ob.is_support ? ob.upper_level : ob.lower_level
                // Corrected to use real-time `close` instead of `close[1]`
                if (ob.is_support and close > mitigation_level) or (not ob.is_support and close < mitigation_level)
                    ob.is_mitigated := true
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level, "Failed: Mitigated after " + str.tostring(bar_index - ob.last_touched_bar) + " bars", color=color.rgb(128,128,128), style=label.style_label_down)
                    else if debugMode
                        label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level, "Mitigated: " + (ob.is_support ? "Support" : "Resistance"), color=color.rgb(128,128,128), style=label.style_label_down)

            // ====================================================================
            // STEP 3 - Update Visuals
            // ====================================================================
            // This code only runs for blocks that are visible and not being removed.
            
            blockAge = bar_index - ob.created_at
            int ageBasedTransparency = applyBlockAgeFading ? math.min(60 + math.round(blockAge * 0.5), 90) : 85

            if ob.is_display_hidden
                if not na(ob.top_price_label)
                    label.delete(ob.top_price_label)
                    ob.top_price_label := na
                if not na(ob.bottom_price_label)
                    label.delete(ob.bottom_price_label)
                    ob.bottom_price_label := na
                if not na(ob.confluence_label)
                    label.delete(ob.confluence_label)
                    ob.confluence_label := na
            else
                box.set_right(ob.box, bar_index + extendObs)
                if showMidline and not na(ob.midline)
                    line.set_x2(ob.midline, bar_index + extendObs)

                if showPriceLabels
                    // Update the labels
                    if not na(ob.top_price_label)
                        label.delete(ob.top_price_label)
                        ob.top_price_label := na
                    if not na(ob.bottom_price_label)
                        label.delete(ob.bottom_price_label)
                        ob.bottom_price_label := na

                    mid_level = (ob.upper_level + ob.lower_level) / 2
                    buy_pct = ob.total_volume > 0 ? (ob.buy_volume / ob.total_volume) * 100 : 0
                    sell_pct = ob.total_volume > 0 ? (ob.sell_volume / ob.total_volume) * 100 : 0
                    buySellIcon = getBuySellIcon(buy_pct, sell_pct)
                    statusText = ob.is_broken ? "⚠️ BROKEN" : ob.is_mitigated ? "⚡ MIT" : "✅"
                    strengthRating = showZoneStrength ? f_getColorCodedStarRating(ob.strength_score) : ""
                    retestInfo = ob.retest_count > 0 ? "🔄 Retests: " + str.tostring(ob.retest_count) + " | ✓ " + str.tostring(math.round(ob.success_rate * 100, 0)) + "%" : ""
                    ageInfo = "⏱️ Age: " + str.tostring(blockAge) + " bars"
                    confluence_info = ""
                    if detectConfluence and highlightConfluence and ob.has_confluence
                        confluence_factors = array.new_string(0)
                        if ob.rsi_confluence
                            array.push(confluence_factors, "RSI")
                        if ob.macd_confluence
                            array.push(confluence_factors, "MACD")
                        if ob.ma_confluence
                            array.push(confluence_factors, "MA")
                        if ob.volume_confluence
                            array.push(confluence_factors, "VOL")
                        if array.size(confluence_factors) > 0
                            confluence_info := "🎯 " + array.join(confluence_factors, ",")

                    labelText = ""
                    if displayModeOpt == "Compact"
                        labelText := (ob.is_support ? "SUP " : "RES ") + str.tostring(mid_level, "#.###") + " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" + (showZoneStrength ? " " + strengthRating : "") + (confluence_info != "" ? " | " + confluence_info : "")
                    else
                        labelText := "📍 " + (ob.is_support ? "SUP " : "RES ") + statusText + " | " + str.tostring(mid_level, "#.###") + " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" + (showZoneStrength ? " | " + strengthRating : "") + "\n" + "📊 VOL: " + formatVolume(ob.total_volume) + " | 💪 " + str.tostring(math.round(total_volume > 0 ? (ob.total_volume / total_volume * 100) : 0, 1)) + "%\n" + buySellIcon + " " + (buy_pct > sell_pct ? "BUY" : "SELL") + " " + str.tostring(math.round(math.max(buy_pct, sell_pct), 1)) + "% | B:" + formatVolume(ob.buy_volume) + " S:" + formatVolume(ob.sell_volume) + (ob.retest_count > 0 ? "\n" + retestInfo : "") + "\n" + ageInfo + (confluence_info != "" ? "\n" + confluence_info : "")

                    if ob.is_support
                        ob.bottom_price_label := label.new(bar_index + extendObs, ob.lower_level, labelText, color=color.new(color.black, 20), style=label.style_label_left, textcolor=color.white, size=orderBlockTextSize, yloc=yloc.price)
                    else
                        ob.top_price_label := label.new(bar_index + extendObs, ob.upper_level, labelText, color=color.new(color.black, 20), style=label.style_label_left, textcolor=color.white, size=orderBlockTextSize, yloc=yloc.price)

                // Update colors and styles
                [updated_fill_color, updated_border_color] = f_getEnhancedColors(ob.is_support, ob.strength_score, ob.total_volume > 0 ? ob.total_volume / ta.sma(volume, 20) : 1.0, ob.has_confluence, ob.is_broken, ob.is_mitigated)
                updated_border_width = f_getBorderWidth(ob.total_volume > 0 ? ob.total_volume / ta.sma(volume, 20) : 1.0, ob.has_confluence)
                pulse_effect = pulseNewBlocks and not ob.is_broken and not ob.is_mitigated ? f_getPulseEffect(ob.created_at, bar_index) : 0
                final_box_color = applyBlockAgeFading ? color.new(updated_fill_color, ageBasedTransparency + pulse_effect) : color.new(updated_fill_color, color.t(updated_fill_color) + pulse_effect)
                border_color = getBuySellColor(ob.total_volume > 0 ? (ob.buy_volume / ob.total_volume) * 100 : 0, ob.total_volume > 0 ? (ob.sell_volume / ob.total_volume) * 100 : 0)
                smart_border_style = ob.is_broken ? line.style_dashed : (blockStyle == "Rounded" ? line.style_solid : (blockStyle == "3D Shadow" ? line.style_dotted : orderBlockBorderStyle))
                
                box.set_bgcolor(ob.box, final_box_color)
                box.set_border_color(ob.box, border_color)
                box.set_border_width(ob.box, updated_border_width)
                box.set_border_style(ob.box, smart_border_style)
                
                if showMidline and not na(ob.midline)
                    [theme_fill, theme_border] = f_getThemeColors(themePreset, ob.is_support)
                    line.set_color(ob.midline, ob.is_support ? theme_fill : theme_border)
                
                array.set(order_blocks, i, ob)

        // Remove ineligible blocks at the end
        if array.size(to_remove) > 0
            for i = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, i)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)
                    if debugMode
                        label.new(bar_index, close, "Removed Block: " + (ob.is_support ? "Support" : "Resistance"), color=color.orange, style=label.style_label_down)

// ====================================================================
// FUNCTIONS FOR BLOCK SORTING (Using a reliable Insertion Sort)
// This non-recursive method is guaranteed to compile and work correctly.
// ====================================================================

// Helper function to calculate the composite score for a single OrderBlock.
_calculateCompositeScore(OrderBlock ob, int current_bar_index) =>
    float recency_score = math.max(0.0, 1.0 - (current_bar_index - ob.created_at) / 500.0)
    float strength_score = ob.strength_score / 10.0
    int confluence_count = (ob.rsi_confluence ? 1 : 0) + (ob.macd_confluence ? 1 : 0) + (ob.ma_confluence ? 1 : 0) + (ob.volume_confluence ? 1 : 0)
    float confluence_score = confluence_count / 4.0
    recency_score * 0.4 + strength_score * 0.4 + confluence_score * 0.2

// Hide overlapping blocks with enhanced priority logic
hideOverlappingBlocks() =>
    if hideOverlaps and array.size(order_blocks) > 1
        to_remove = array.new_int(0)
        
        for i = array.size(order_blocks) - 1 to 1 by 1
            if not array.includes(to_remove, i)
                ob1 = array.get(order_blocks, i)
                
                for j = i - 1 to 0 by 1
                    if not array.includes(to_remove, j)
                        ob2 = array.get(order_blocks, j)
                        
                        if ob1.lower_level <= ob2.upper_level and ob1.upper_level >= ob2.lower_level
                            bool keep_ob1 = false
                            
                            // Priority Logic
                            if ob2.is_broken and not ob1.is_broken
                                keep_ob1 := true
                            else if ob1.is_broken and not ob2.is_broken
                                keep_ob1 := false
                            else if ob1.strength_score > ob2.strength_score
                                keep_ob1 := true
                            else if ob2.strength_score > ob1.strength_score
                                keep_ob1 := false
                            else if ob1.has_confluence and not ob2.has_confluence
                                keep_ob1 := true
                            else if ob2.has_confluence and not ob1.has_confluence
                                keep_ob1 := false
                            else if ob1.total_volume > ob2.total_volume
                                keep_ob1 := true
                            else
                                keep_ob1 := false
                            
                            if keep_ob1
                                array.push(to_remove, j)
                            else
                                array.push(to_remove, i)
                                // The 'break' statement that was here has been removed.
        
        // Remove the weaker blocks (rest of the function is the same)
        if array.size(to_remove) > 0
            for k = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, k)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)

// Main function to select top blocks for display.
// This function sorts the blocks using a reliable, non-recursive Insertion Sort.
selectTopBlocksForDisplay() =>
    int num_blocks = array.size(order_blocks)
    if num_blocks <= max_blocks
        // No blocks to hide
        array.new<OrderBlock>()
    else
        // Create a mutable copy for sorting
        array<OrderBlock> display_blocks = array.copy(order_blocks)
        
        // Insertion sort with bounds checking
        for i = 1 to num_blocks - 1
            OrderBlock key_block = array.get(display_blocks, i)
            float key_score = _calculateCompositeScore(key_block, bar_index)
            int j = i - 1
            
            // Safe comparison loop
            while j >= 0
                if j >= array.size(display_blocks)  // Bounds check
                    break
                    
                OrderBlock compare_block = array.get(display_blocks, j)
                float compare_score = _calculateCompositeScore(compare_block, bar_index)
                
                if compare_score < key_score
                    if j+1 < array.size(display_blocks)  // Bounds check
                        array.set(display_blocks, j + 1, compare_block)
                    j := j - 1
                else
                    break
            
            // Safe insertion
            if j+1 < array.size(display_blocks)
                array.set(display_blocks, j + 1, key_block)

        // Return blocks beyond max_blocks to hide
        if max_blocks < array.size(display_blocks)
            array.slice(display_blocks, max_blocks)
        else
            array.new<OrderBlock>()
// ====================================================================
// MAIN LOGIC
// ====================================================================
// Reset filtered blocks tracking arrays each bar
array.clear(filtered_by_create)
array.clear(filtered_by_update)

// Clean up any orphaned visual elements on first bar
if barstate.isfirst
    // Clear any existing boxes and labels that might be left over
    int blocks_count = array.size(order_blocks)
    if blocks_count > 0  // Only process if array has elements
        for i = 0 to blocks_count - 1
            ob = array.get(order_blocks, i)
            f_cleanupOrderBlock(ob)
    // Always clear the array, even if it's empty
    array.clear(order_blocks)

// Calculate ATR once per bar for potential reuse
var float current_atr_value = na
current_atr_value := ta.atr(dynamicATRPeriod)

volSMA := ta.sma(math.abs(upAndDownVolume()), vol_len)

float ema200_val = ta.ema(ema200_source, ema200_length)

if debugMode
    // Extract function calls for consistency
    int debug_regime = f_detectMarketRegime()

    // Enhanced debug info for troubleshooting
    string regime_text = debug_regime == 1 ? "Trending" : debug_regime == 2 ? "Volatile" : "Ranging"

    string debug_info = "Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Optimized)" : " (Manual)") +
                 "\nRegime: " + regime_text

    label.new(bar_index, high, debug_info, style=label.style_none, color=color.new(color.white, 0), textcolor=color.white)

// Calculate support and resistance levels using optimized lookback
[supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt, sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol] = calcSupportResistance(close, effectiveLookback, math.abs(upAndDownVolume()), volSMA, atr)

// In main logic for support blocks:
// The trend filter "(not useTrendFilter or close > ema200_val)" has been removed from this line.
if not na(supportLevel) and Vol > volSMA * volMultiplier
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to effectiveLookback
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(true, supportLevel, supportLevelAlt, totalVol, buyVol, sellVol, sup_color)

// In main logic for resistance blocks:
// The trend filter "(not useTrendFilter or close < ema200_val)" has been removed from this line.
if not na(resistanceLevel) and Vol > volSMA * volMultiplier
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to effectiveLookback
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(false, resistanceLevel, resistanceLevelAlt, totalVol, buyVol, sellVol, res_color)

// First, hide overlapping blocks to reduce visual clutter
hideOverlappingBlocks()

// Then, update all blocks (including hidden ones) for analysis
updateOrderBlocks()

// Finally, select top blocks for display based on our criteria
// This allows us to analyze more blocks but only display the most relevant ones
blocks_to_hide = selectTopBlocksForDisplay()

// Hide visual elements for blocks that exceed the display limit
if array.size(blocks_to_hide) > 0
    for ob in blocks_to_hide
        // Instead of trying to hide existing elements, we'll remove them and track that we've done so
        // This is more reliable than trying to make them transparent
        f_cleanupOrderBlock(ob)

        // Mark the block as hidden for display purposes but kept for analysis
        ob.is_display_hidden := true  // Use our new dedicated flag

        // Make sure we don't confuse this with actually mitigated blocks
        ob.is_mitigated := false

    if debugMode and array.size(blocks_to_hide) > 0
        label.new(bar_index, close, "Hidden for display: " + str.tostring(array.size(blocks_to_hide)) + " blocks",
                 color=color.new(color.blue, 70), style=label.style_label_down, textcolor=color.white)

// Count filtered blocks from the tracking arrays
int filtered_count = 0

// Only process arrays if they have elements
if array.size(filtered_by_create) > 0
    for i = 0 to array.size(filtered_by_create) - 1
        if array.get(filtered_by_create, i)
            filtered_count += 1

if array.size(filtered_by_update) > 0
    for i = 0 to array.size(filtered_by_update) - 1
        if array.get(filtered_by_update, i)
            filtered_count += 1

// Update the global filtered blocks count
filtered_blocks_count := filtered_count

// Add message if orderblocks are being created but not displayed
// Show a more prominent warning in debug mode, and a subtle one in normal mode
if array.size(order_blocks) == 0 and filtered_blocks_count > 0
    if debugMode
        // Detailed warning in debug mode
        label.new(bar_index, high + atr * 3, "⚠️ All orderblocks filtered out by star rating filter! Try lowering the minimum star rating.",
                  color=color.new(color.red, 20),
                  style=label.style_label_down,
                  textcolor=color.white,
                  size=size.normal)
    else
        // Subtle warning in normal mode - only show once every 20 bars to avoid clutter
        if bar_index % 20 == 0
            label.new(bar_index, high + atr * 1.5, "⚠️ Blocks filtered by " + star_selection + " rating",
                      color=color.new(color.black, 70),
                      style=label.style_label_down,
                      textcolor=color.new(color.white, 20),
                      size=size.small)

// ====================================================================
// REVERSAL PATTERN DETECTION
// ====================================================================
bullish_reversal = (high[1] > high) and (close[1] < close) and (open[1] < open) and barstate.isconfirmed
bearish_reversal = (low[1] < low) and (close[1] > close) and (open[1] > open) and barstate.isconfirmed

// Enhanced reversal triangles with better visibility
plotshape(
     series = showReversalTriangles and bullish_reversal ? 1 : na,
     title="Bullish Reversal",
     location=location.abovebar,
     color=bullishReversalColor,
     style=shape.triangledown,
     size=size.tiny
     )

plotshape(
     series = showReversalTriangles and bearish_reversal ? 1 : na,
     title="Bearish Reversal",
     location=location.belowbar,
     color=bearishReversalColor,
     style=shape.triangleup,
     size=size.tiny
     )

// ====================================================================
// DEBUG MODE (Enhanced Debug Information)
// ====================================================================
// Create star rating statistics panel
// Calculate number of rows needed based on min_star_rating (header + ratings from min to 5 + description)
var int table_rows = 7  // Default value for initialization
table_rows := 3 + (6 - min_star_rating)  // Header + ratings from min to 5 + description

var table starStatsTable = table.new(
     position.bottom_left,
     3, table_rows,
     bgcolor = color.new(color.black, 30),
     border_width = 1,
     border_color = color.new(color.white, 70)
     )

// Initialize the star stats table
table.cell(starStatsTable, 0, 0, "⭐ Rating", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
table.cell(starStatsTable, 1, 0, "Count", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
table.cell(starStatsTable, 2, 0, "Success", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

// Add description of success criteria and analysis information
table.cell(starStatsTable, 0, table_rows - 1, "Success: Block intact " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars after touch" +
          "\nAnalyzing up to " + str.tostring(analysis_blocks) + " blocks",
          text_color=color.rgb(0,255,128), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

// Initialize star rating counts and success tracking
var int[] star_counts = array.new_int(5, 0)
var int[] star_success = array.new_int(5, 0)
var int[] star_total = array.new_int(5, 0)

// Reset counts each bar
for i = 0 to 4
    array.set(star_counts, i, 0)

// Count blocks by star rating - include all blocks kept for analysis
if array.size(order_blocks) > 0
    for ob in order_blocks
        // Include all blocks in the statistics, including those hidden for display
        star_rating = ob.strength_score >= 8.0 ? 5 :
                     ob.strength_score >= 6.0 ? 4 :
                     ob.strength_score >= 4.0 ? 3 :
                     ob.strength_score >= 2.0 ? 2 : 1

        array.set(star_counts, star_rating - 1, array.get(star_counts, star_rating - 1) + 1)

        // Track success/failure for performance stats
        // Include all blocks with success data, regardless of display status
        if ob.was_successful and ob.success_evaluated
            array.set(star_success, star_rating - 1, array.get(star_success, star_rating - 1) + 1)
            array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)
        else if ob.is_broken or (ob.success_evaluated and not ob.was_successful)
            array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)

    // Update star stats table
    // Only show ratings that meet or exceed the user's selected minimum star rating
    int table_row = 1  // Start at row 1 (after header)

    for i = min_star_rating - 1 to 4  // Start from min_star_rating-1 (array index) to 4 (5 stars)
        stars = i == 0 ? "⭐" : i == 1 ? "⭐⭐" : i == 2 ? "⭐⭐⭐" : i == 3 ? "⭐⭐⭐⭐" : "⭐⭐⭐⭐⭐"
        count = array.get(star_counts, i)

        // Calculate success rate
        success_rate = array.get(star_total, i) > 0 ?
                      math.round(array.get(star_success, i) / array.get(star_total, i) * 100, 0) :
                      0

        success_text = array.get(star_total, i) > 0 ?
                      str.tostring(success_rate) + "%" :
                      "N/A"

        // Set cell colors based on star rating
        color text_color = i == 4 ? color.rgb(0,255,0) :
                          i == 3 ? color.rgb(144,238,144) :
                          i == 2 ? color.rgb(255,255,0) :
                          i == 1 ? color.rgb(255,165,0) :
                          color.rgb(255,69,0)

        table.cell(starStatsTable, 0, table_row, stars, text_color=text_color, text_size=tableTextSize)
        table.cell(starStatsTable, 1, table_row, str.tostring(count), text_color=color.white, text_size=tableTextSize)
        table.cell(starStatsTable, 2, table_row, success_text, text_color=success_rate >= 70 ? color.rgb(0,255,0) :
                                                                     success_rate >= 50 ? color.rgb(255,255,0) :
                                                                     color.rgb(255,69,0), text_size=tableTextSize)
        table_row += 1  // Move to next row

if debugMode
    // Improved debug label with better styling
    var label debug_label = label.new(
         bar_index, high + atr * 2,
         "",
         color=color.new(color.black, 20),
         style=label.style_label_down,
         textcolor=color.white,
         size=size.normal
         )

    // Create a more comprehensive debug panel
    // Extract function calls for consistency
    int regime = f_detectMarketRegime()
    float adaptive_box_size = f_getAdaptiveBoxSize() // f_getAdaptiveBoxSize now calculates ATR internally
    // float current_atr = current_atr_value // This might be redundant here if adaptive_box_size is used directly

    // Get market regime for display
    string regime_text = regime == 1 ? "Trending" : regime == 2 ? "Volatile" : "Ranging"

    // Get box size
    float effective_box_size = 0.0
    if useDynamicBox
        if adaptiveBoxSize
            effective_box_size = adaptive_box_size // adaptive_box_size is the actual size
        else
            effective_box_size = atr * dynamicBoxMultiplier // atr is global ta.atr(atr_len)
    else
        effective_box_size = atr * box_width // atr is global ta.atr(atr_len)

    // Count visible and hidden blocks using our dedicated flag
    int visible_blocks = 0
    int hidden_blocks = 0
    for ob in order_blocks
        if ob.is_display_hidden
            hidden_blocks += 1
        else
            visible_blocks += 1

    // Count blocks with success data
    int blocks_with_success = 0
    int blocks_with_failure = 0
    for ob in order_blocks
        if ob.success_evaluated
            if ob.was_successful
                blocks_with_success += 1
            else
                blocks_with_failure += 1

    string debug_text = "🐞 Debug Info v4.0:\n" +
                 "📊 OB Count: " + str.tostring(visible_blocks) + " visible / " + str.tostring(hidden_blocks) + " hidden / " + str.tostring(array.size(order_blocks)) + " total\n" +
                 "🔍 Analysis Blocks: " + str.tostring(analysis_blocks) + " max\n" +
                 "📊 Success Data: " + str.tostring(blocks_with_success) + " success / " + str.tostring(blocks_with_failure) + " failure\n" +
                 "📈 Vol SMA: " + str.tostring(volSMA) + "\n" +
                 "📏 ATR: " + str.tostring(atr) + "\n" +
                 "🔍 Market Regime: " + regime_text + "\n" +
                 "🔍 Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Optimized from " + str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange) +
                  ", Manual: " + str.tostring(lookbackPeriod) + ")" : " (Manual)") + "\n" +
                 "📏 Effective Box Size: " + str.tostring(math.round(effective_box_size, 2)) + "×ATR\n"

    // Add information about the most recent order block if available
    if array.size(order_blocks) > 0
        OrderBlock latest_ob = array.get(order_blocks, 0)
        debug_text += "📌 Last OB: " + (latest_ob.is_support ? "Support" : "Resistance") +
                     " at " + str.tostring(latest_ob.is_support ? latest_ob.upper_level : latest_ob.lower_level) + "\n" +
                     "⏱️ Created: bar " + str.tostring(latest_ob.created_at) + "\n" +
                     "📊 Volume: " + formatVolume(latest_ob.total_volume) + "\n" +
                     "💪 Strength: " + str.tostring(math.round(latest_ob.strength_score, 1)) + "/10\n" +
                     "🎯 Confluence: " + (latest_ob.has_confluence ? "Yes" : "No") + "\n" +
                     "🔄 Retests: " + str.tostring(latest_ob.retest_count) +
                     (latest_ob.retest_count > 0 ? " (Success: " + str.tostring(math.round(latest_ob.success_rate * 100, 0)) + "%)" : "") + "\n" +
                     "🔄 Status: " + (latest_ob.is_broken ? "Broken" : latest_ob.is_mitigated ? "Mitigated" : "Active") + "\n"

    // Add system settings information
    debug_text += "🔍 Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Opt: " + str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange) +
                  ", Manual: " + str.tostring(lookbackPeriod) + ", Last Opt: " + str.tostring(bar_index - lastOptimizationBar) + " bars ago)" : " (Manual)") + "\n" +
                 "📏 Box Width: " + str.tostring(useDynamicBox ? (na(current_atr_value) ? atr * dynamicBoxMultiplier : current_atr_value * dynamicBoxMultiplier) : atr * box_width) + "\n" +
                 "🧠 Smart Filter: " + str.tostring(useSmartFiltering) + "\n" +
                 "⏰ Time Filter: " + (enableTimeFilter ? "On (" + str.tostring(maxBlockAge) + " bars)" : "Off") + "\n" +
                 "⭐ Min Star Rating: " + str.tostring(min_star_rating) + " " +
                 (min_star_rating == 5 ? "⭐⭐⭐⭐⭐" :
                  min_star_rating == 4 ? "⭐⭐⭐⭐" :
                  min_star_rating == 3 ? "⭐⭐⭐" :
                  min_star_rating == 2 ? "⭐⭐" : "⭐") + "\n" +
                 "🎯 Confluence: " + str.tostring(detectConfluence)

    label.set_text(debug_label, debug_text)
    label.set_xy(debug_label, bar_index, high + atr * 2)

    // Add a statistics table if we have enough data
    if array.size(order_blocks) >= 3
        var table statsTable = table.new(
             position.bottom_right,
             3, 3,
             bgcolor = color.new(color.black, 30),
             border_width = 1,
             border_color = color.new(color.white, 70)
             )

        // Count blocks by type
        int support_count = 0
        int resistance_count = 0
        int confluence_count = 0
        int broken_count = 0

        for ob in order_blocks
            if ob.is_support
                support_count += 1
            else
                resistance_count += 1

            if ob.has_confluence
                confluence_count += 1

            if ob.is_broken
                broken_count += 1

        // Update table with statistics
        table.cell(statsTable, 0, 0, "📊 Statistics", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
        table.cell(statsTable, 1, 0, "Support", text_color=color.rgb(0,220,128), text_size=tableTextSize)
        table.cell(statsTable, 2, 0, str.tostring(support_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 1, "Resistance", text_color=color.rgb(255,100,100), text_size=tableTextSize)
        table.cell(statsTable, 2, 1, str.tostring(resistance_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 2, "Confluence", text_color=color.rgb(255,215,0), text_size=tableTextSize)
        table.cell(statsTable, 2, 2, str.tostring(confluence_count), text_color=color.white, text_size=tableTextSize)

// Lookback Optimization Display Table
if enableLookbackOptimization and showOptimizedValue
    var table lookbackOptTable = table.new(
         position.top_left,
         2, 7,
         bgcolor = color.new(color.black, 30),
         border_width = 1,
         border_color = color.new(color.white, 70)
         )

    // Header
    table.cell(lookbackOptTable, 0, 0, "🔧 Lookback Optimization", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 0, "", bgcolor=color.new(color.black, 20))

    // Manual vs Optimized comparison
    table.cell(lookbackOptTable, 0, 1, "Manual Value:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 1, str.tostring(lookbackPeriod), text_color=color.rgb(255,255,255), text_size=tableTextSize)

    // Current optimized value
    table.cell(lookbackOptTable, 0, 2, "Optimized Value:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    color optimizedColor = optimizedLookback != lookbackPeriod ? color.rgb(0,255,128) : color.rgb(255,255,255)
    string optimizedText = str.tostring(optimizedLookback) + " (Effective: " + str.tostring(effectiveLookback) + ")"
    table.cell(lookbackOptTable, 1, 2, optimizedText, text_color=optimizedColor, text_size=tableTextSize)

    // Range being tested
    table.cell(lookbackOptTable, 0, 3, "Test Range:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 3, str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange), text_color=color.white, text_size=tableTextSize)

    // Last optimization info
    table.cell(lookbackOptTable, 0, 4, "Last Optimized:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    int barsSinceOpt = bar_index - lastOptimizationBar
    table.cell(lookbackOptTable, 1, 4, str.tostring(barsSinceOpt) + " bars ago", text_color=color.rgb(255,165,0), text_size=tableTextSize)

    // Update frequency
    table.cell(lookbackOptTable, 0, 5, "Next Update:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    int updateFreq = bar_index < 50 ? 5 : 10
    int barsUntilUpdate = updateFreq - barsSinceOpt
    table.cell(lookbackOptTable, 1, 5, str.tostring(math.max(0, barsUntilUpdate)) + " bars", text_color=color.rgb(255,165,0), text_size=tableTextSize)

    // Debug: Show internal state
    table.cell(lookbackOptTable, 0, 6, "Debug State:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    string debugState = "Opt=" + str.tostring(na(optimizedLookback) ? -1 : optimizedLookback) + ", Eff=" + str.tostring(effectiveLookback) + ", En=" + str.tostring(enableLookbackOptimization)
    table.cell(lookbackOptTable, 1, 6, debugState, text_color=color.rgb(255,255,0), text_size=tableTextSize)

// Star rating filter table
var table starRatingTable = table.new(
     position.top_right,  // Changed to top_left to avoid overlap with lookback table
     1, 4,  // Increased rows to 4 to show dynamic adjustment info
     bgcolor = color.new(color.black, 30),
     border_width = 1,
     border_color = color.new(color.white, 70)
     )

// Get dynamic adjustment info
float base_threshold = min_star_rating == 5 ? 8.0 :
                      min_star_rating == 4 ? 6.0 :
                      min_star_rating == 3 ? 4.0 :
                      min_star_rating == 2 ? 2.0 : 0.0
float actual_threshold = f_getStarRatingThreshold(min_star_rating)
string dynamic_status = ""

if dynamicStarRating and min_star_rating > 1
    float adjustment_pct = (actual_threshold / base_threshold - 1.0) * 100
    string adjustment_dir = adjustment_pct < 0 ? "↓" : adjustment_pct > 0 ? "↑" : "="
    dynamic_status := "Dynamic: " + adjustment_dir + " " + str.tostring(math.abs(math.round(adjustment_pct, 1))) + "%"
else
    dynamic_status := "Dynamic: Off"

if debugMode
    // Update star rating filter table
    table.cell(
         starRatingTable, 0, 0,
         "⭐ Min Star Rating",
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 30)
         )

    table.cell(
         starRatingTable, 0, 1,
         (min_star_rating == 5 ? f_getColorCodedStarRating(8.0) :
         min_star_rating == 4 ? f_getColorCodedStarRating(6.0) :
         min_star_rating == 3 ? f_getColorCodedStarRating(4.0) :
         min_star_rating == 2 ? f_getColorCodedStarRating(2.0) : f_getColorCodedStarRating(1.0)),
         text_color=color.rgb(255,215,0),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 50)
         )

    // Add filtered blocks count
    table.cell(
         starRatingTable, 0, 2,
         "Filtered: " + str.tostring(filtered_blocks_count),
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )

    // Add dynamic adjustment info
    table.cell(
         starRatingTable, 0, 3,
         dynamic_status,
         text_color=dynamicStarRating ? color.rgb(0,255,128) : color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )

// ====================================================================
// MOVING AVERAGE PLOTTING
// ====================================================================
// Enhanced moving averages with improved visibility
plot(
     show_sma ? ta.sma(sma_source, sma_length) : na,
     "SMA",
     color=sma_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema50 ? ta.ema(ema50_source, ema50_length) : na,
     "EMA 50",
     color=ema50_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema200 ? ta.ema(ema200_source, ema200_length) : na,
     "EMA 200",
     color=ema200_color,
     linewidth=2,
     style=plot.style_line
     )

// ====================================================================
// ALERT SELECTION SETTINGS
// ====================================================================
group_alerts = "🚨 Alert Settings"
enableNewBlockAlert = input.bool(false, "🆕 New Block Alert", group=group_alerts, tooltip="Alert when new order block is created")
enableDetailedBlockAlert = input.bool(false, "📝 Detailed Block Alert", group=group_alerts, tooltip="Detailed information about new blocks")
enableHighStrengthAlert = input.bool(false, "💎 High Strength Alert (4+ Stars)", group=group_alerts, tooltip="Alert for high quality blocks only")
enableConfluenceAlert = input.bool(false, "🎯 Confluence Alert", group=group_alerts, tooltip="Alert when confluence zones are created")
enablePremiumAlert = input.bool(false, "👑 Premium Alert (5 Stars + Confluence)", group=group_alerts, tooltip="Alert for highest quality blocks")
enableRetestAlert = input.bool(false, "🔄 Zone Retest Alert", group=group_alerts, tooltip="Alert when price retests a zone")
enableBreakAlert = input.bool(false, "💥 Block Break Alert", group=group_alerts, tooltip="Alert when blocks are broken")
enableMitigationAlert = input.bool(false, "⚡ Mitigation Alert", group=group_alerts, tooltip="Alert when blocks are mitigated")
enableReversalAlert = input.bool(false, "🔄 Reversal Pattern Alert", group=group_alerts, tooltip="Alert for bullish/bearish reversals")
enableRegimeChangeAlert = input.bool(false, "📊 Market Regime Alert", group=group_alerts, tooltip="Alert when market regime changes")
enableVolumeAlert = input.bool(false, "📊 Volume Spike Alert", group=group_alerts, tooltip="Alert for unusual volume")
enableComboAlert = input.bool(false, "🎯 Combo Setup Alert", group=group_alerts, tooltip="Alert for perfect setups (block + reversal)")
enableCriticalAlert = input.bool(false, "🚨 Critical Break Alert", group=group_alerts, tooltip="Alert for important level breaks")
enableQuickAlert = input.bool(false, "📱 Quick Mobile Alert", group=group_alerts, tooltip="Short format alerts for mobile")

// ====================================================================
// ENHANCED ALERT CONDITIONS - COMPREHENSIVE ALERT SYSTEM
// ====================================================================

// ====================================================================
// VARIABLES FOR ALERT TRACKING
// ====================================================================
var bool newBlockCreated = false
var float newBlockLevel = na
var float newBlockStrength = na
var bool newBlockHasConfluence = false
var int newBlockStarRating = 0
var bool newBlockIsSupport = false

var bool blockBroken = false
var float brokenBlockLevel = na
var bool brokenBlockIsSupport = false
var bool blockMitigated = false
var float mitigatedBlockLevel = na
var bool mitigatedBlockIsSupport = false

var bool zoneRetest = false
var float retestBlockLevel = na
var bool retestBlockIsSupport = false
var int retestCount = 0

var bool highStrengthBlock = false
var bool confluenceZoneCreated = false

// ====================================================================
// NEW BLOCK CREATION ALERTS
// ====================================================================

// Reset alert flags
newBlockCreated := false
blockBroken := false
blockMitigated := false
zoneRetest := false
highStrengthBlock := false
confluenceZoneCreated := false

// Check if a new block was just created
if array.size(order_blocks) > 0
    OrderBlock latestBlock = array.get(order_blocks, 0)
    
    // Check if this block was created on the current bar
    if latestBlock.created_at == bar_index
        newBlockCreated := true
        newBlockIsSupport := latestBlock.is_support
        newBlockLevel := latestBlock.is_support ? latestBlock.upper_level : latestBlock.lower_level
        newBlockStrength := latestBlock.strength_score
        newBlockHasConfluence := latestBlock.has_confluence
        
        // Calculate star rating
        newBlockStarRating := latestBlock.strength_score >= 8.0 ? 5 :
                             latestBlock.strength_score >= 6.0 ? 4 :
                             latestBlock.strength_score >= 4.0 ? 3 :
                             latestBlock.strength_score >= 2.0 ? 2 : 1
        
        // Check for high strength block (4+ stars)
        highStrengthBlock := newBlockStarRating >= 4
        
        // Check for confluence zone
        confluenceZoneCreated := latestBlock.has_confluence

// ====================================================================
// BLOCK STATE CHANGE ALERTS
// ====================================================================

// Check for broken or mitigated blocks
if array.size(order_blocks) > 0
    for ob in order_blocks
        // Check for newly broken blocks
        if ob.is_broken and not ob.is_mitigated and ob.last_touched_bar == bar_index
            blockBroken := true
            brokenBlockIsSupport := ob.is_support
            brokenBlockLevel := ob.is_support ? ob.lower_level : ob.upper_level
        
        // Check for newly mitigated blocks
        if ob.is_mitigated and ob.last_touched_bar == bar_index
            blockMitigated := true
            mitigatedBlockIsSupport := ob.is_support
            mitigatedBlockLevel := ob.is_support ? ob.upper_level : ob.lower_level
        
        // Check for zone retests
        if not ob.is_broken and not ob.is_mitigated
            price_range = math.abs(ob.upper_level - ob.lower_level)
            is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25) or
                          (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25)
            
            if is_retesting
                zoneRetest := true
                retestBlockIsSupport := ob.is_support
                retestBlockLevel := ob.is_support ? ob.upper_level : ob.lower_level
                retestCount := ob.retest_count + 1
                break

// ====================================================================
// MARKET CONDITION VARIABLES
// ====================================================================
var int previous_regime = na
current_regime_int = f_detectMarketRegime()
regime_changed = not na(previous_regime) and previous_regime != current_regime_int
previous_regime := current_regime_int

volume_spike = volume > ta.sma(volume, 20) * volumeSpikeFactor

// Combo conditions
perfect_setup = newBlockCreated and (bullish_reversal or bearish_reversal)

// Confluence retest
confluence_retest = zoneRetest and array.size(order_blocks) > 0
var bool confluence_retest_confirmed = false
if confluence_retest
    for ob in order_blocks
        price_range = math.abs(ob.upper_level - ob.lower_level)
        is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25) or
                      (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25)
        if is_retesting and ob.has_confluence
            confluence_retest_confirmed := true
            break

// Critical level break
var bool critical_break = false
if blockBroken and array.size(order_blocks) > 0
    for ob in order_blocks
        if ob.is_broken and ob.last_touched_bar == bar_index and ob.strength_score >= 6.0
            critical_break := true
            break

//────────────────────────────────────────────────────────────
// ALERT MESSAGE CONSTRUCTION & TRIGGERING
//────────────────────────────────────────────────────────────
generateAlertMessage() =>
    string message = ""
    
    // New Block Creation Alerts
    if enableNewBlockAlert and newBlockCreated
        if newBlockIsSupport
            message := message + "🆕 New Support Block at " + str.tostring(newBlockLevel, "#.####") + " | ⭐" + str.tostring(newBlockStarRating) + "/5 | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
        else
            message := message + "🆕 New Resistance Block at " + str.tostring(newBlockLevel, "#.####") + " | ⭐" + str.tostring(newBlockStarRating) + "/5 | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
    
    // High Strength Block Alerts
    if enableHighStrengthAlert and highStrengthBlock
        if newBlockIsSupport
            message := message + "💎 HIGH QUALITY SUPPORT at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐+ rating | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
        else
            message := message + "💎 HIGH QUALITY RESISTANCE at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐+ rating | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
    
    // Confluence Zone Alerts
    if enableConfluenceAlert and confluenceZoneCreated
        if newBlockIsSupport
            message := message + "🎯 CONFLUENCE SUPPORT ZONE at " + str.tostring(newBlockLevel, "#.####") + " | Multiple technical factors align!\n"
        else
            message := message + "🎯 CONFLUENCE RESISTANCE ZONE at " + str.tostring(newBlockLevel, "#.####") + " | Multiple technical factors align!\n"
    
    // Premium Quality Block Alerts
    if enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence
        if newBlockIsSupport
            message := message + "👑 PREMIUM SUPPORT BLOCK at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐⭐ + CONFLUENCE | Highest Quality Zone!\n"
        else
            message := message + "👑 PREMIUM RESISTANCE BLOCK at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐⭐ + CONFLUENCE | Highest Quality Zone!\n"
    
    // Zone Retest Alerts
    if enableRetestAlert and zoneRetest
        if retestBlockIsSupport
            message := message + "🔄 Support Zone Retest at " + str.tostring(retestBlockLevel, "#.####") + " | Retest #" + str.tostring(retestCount) + " | Watch for reaction!\n"
        else
            message := message + "🔄 Resistance Zone Retest at " + str.tostring(retestBlockLevel, "#.####") + " | Retest #" + str.tostring(retestCount) + " | Watch for reaction!\n"
    
    // Block Break Alerts
    if enableBreakAlert and blockBroken
        if brokenBlockIsSupport
            message := message + "💥 SUPPORT BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | Watch for retest as new resistance level\n"
        else
            message := message + "💥 RESISTANCE BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | Watch for retest as new support level\n"
    
    // Block Mitigation Alerts
    if enableMitigationAlert and blockMitigated
        if mitigatedBlockIsSupport
            message := message + "⚡ Support Block MITIGATED at " + str.tostring(mitigatedBlockLevel, "#.####") + " | Full price reversal complete\n"
        else
            message := message + "⚡ Resistance Block MITIGATED at " + str.tostring(mitigatedBlockLevel, "#.####") + " | Full price reversal complete\n"
    
    // Reversal Pattern Alerts
    if enableReversalAlert
        if bullish_reversal
            message := message + "🔼 BULLISH REVERSAL detected at " + str.tostring(close, "#.####") + " | Potential upward move\n"
        if bearish_reversal
            message := message + "🔽 BEARISH REVERSAL detected at " + str.tostring(close, "#.####") + " | Potential downward move\n"
    
    // Market Regime Change Alerts
    if enableRegimeChangeAlert and regime_changed
        regime_text = current_regime_int == 1 ? "TRENDING" : current_regime_int == 2 ? "VOLATILE" : "RANGING"
        message := message + "📊 Market Regime Changed to: " + regime_text + " | Adjust strategy accordingly\n"
    
    // Volume Spike Alerts
    if enableVolumeAlert and volume_spike
        message := message + "📊 VOLUME SPIKE detected | " + str.tostring(math.round(volumeSpikeFactor, 1)) + "x average volume | Watch for new order blocks\n"
    
    // Perfect Setup Alerts (New block + Reversal)
    if enableComboAlert and perfect_setup
        if newBlockIsSupport and bullish_reversal
            message := message + "🎯 PERFECT BULLISH SETUP | New Support + Bullish Reversal at " + str.tostring(newBlockLevel, "#.####") + " | High probability!\n"
        if not newBlockIsSupport and bearish_reversal
            message := message + "🎯 PERFECT BEARISH SETUP | New Resistance + Bearish Reversal at " + str.tostring(newBlockLevel, "#.####") + " | High probability!\n"
    
    // Confluence Retest Alerts
    if enableRetestAlert and confluence_retest_confirmed
        message := message + "🎯 CONFLUENCE ZONE RETEST at " + str.tostring(retestBlockLevel, "#.####") + " | Multiple factors aligned | High probability reaction!\n"
    
    // Critical Level Break Alerts
    if enableCriticalAlert and critical_break
        if brokenBlockIsSupport
            message := message + "🚨 CRITICAL SUPPORT BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | High strength level breached | Significant move expected!\n"
        else
            message := message + "🚨 CRITICAL RESISTANCE BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | High strength level breached | Significant move expected!\n"
    
    // Quick Mobile Alerts
    if enableQuickAlert
        if newBlockCreated
            block_type = newBlockIsSupport ? "SUP" : "RES"
            message := message + "📦 " + block_type + " " + str.tostring(newBlockLevel, "#.####") + " ⭐" + str.tostring(newBlockStarRating) + "\n"
        if blockBroken
            broken_type = brokenBlockIsSupport ? "SUP" : "RES"
            message := message + "⚡💥 BREAK " + broken_type + " " + str.tostring(brokenBlockLevel, "#.####") + "\n"
    
    message

alertMessage = generateAlertMessage()
if alertMessage != "" and barstate.isconfirmed
    alert(alertMessage, alert.freq_once_per_bar_close)

// Individual Alert Conditions for specific triggers
alertcondition(enableNewBlockAlert and newBlockCreated and newBlockIsSupport,
          title="New Support Block",
          message="New Support Block created with high quality metrics")

alertcondition(enableNewBlockAlert and newBlockCreated and not newBlockIsSupport,
          title="New Resistance Block", 
          message="New Resistance Block created with high quality metrics")

alertcondition(enableHighStrengthAlert and highStrengthBlock and newBlockIsSupport,
          title="High Quality Support Block",
          message="High Quality Support Block detected with 4+ star rating")

alertcondition(enableHighStrengthAlert and highStrengthBlock and not newBlockIsSupport,
          title="High Quality Resistance Block",
          message="High Quality Resistance Block detected with 4+ star rating")

alertcondition(enableConfluenceAlert and confluenceZoneCreated and newBlockIsSupport,
          title="Confluence Support Zone",
          message="Confluence Support Zone detected with multiple technical factors")

alertcondition(enableConfluenceAlert and confluenceZoneCreated and not newBlockIsSupport,
          title="Confluence Resistance Zone",
          message="Confluence Resistance Zone detected with multiple technical factors")

alertcondition(enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence and newBlockIsSupport,
          title="Premium Support Block",
          message="Premium Support Block - 5 stars with confluence factors")

alertcondition(enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence and not newBlockIsSupport,
          title="Premium Resistance Block",
          message="Premium Resistance Block - 5 stars with confluence factors")

alertcondition(enableRetestAlert and zoneRetest and retestBlockIsSupport,
          title="Support Zone Retest",
          message="Support Zone being retested - watch for price reaction")

alertcondition(enableRetestAlert and zoneRetest and not retestBlockIsSupport,
          title="Resistance Zone Retest",
          message="Resistance Zone being retested - watch for price reaction")

alertcondition(enableBreakAlert and blockBroken and brokenBlockIsSupport,
          title="Support Block Broken",
          message="Support Block has been broken - potential trend change")

alertcondition(enableBreakAlert and blockBroken and not brokenBlockIsSupport,
          title="Resistance Block Broken",
          message="Resistance Block has been broken - potential trend change")

alertcondition(enableMitigationAlert and blockMitigated and mitigatedBlockIsSupport,
          title="Support Block Mitigated",
          message="Support Block has been fully mitigated")

alertcondition(enableMitigationAlert and blockMitigated and not mitigatedBlockIsSupport,
          title="Resistance Block Mitigated",
          message="Resistance Block has been fully mitigated")

alertcondition(enableReversalAlert and bullish_reversal,
          title="Bullish Reversal Pattern",
          message="Bullish Reversal Pattern detected - potential upward move")

alertcondition(enableReversalAlert and bearish_reversal,
          title="Bearish Reversal Pattern",
          message="Bearish Reversal Pattern detected - potential downward move")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 1,
          title="Market Now Trending",
          message="Market regime changed to Trending - adjust strategy")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 2,
          title="Market Now Volatile", 
          message="Market regime changed to Volatile - adjust strategy")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 0,
          title="Market Now Ranging",
          message="Market regime changed to Ranging - adjust strategy")

alertcondition(enableVolumeAlert and volume_spike,
          title="Volume Spike Detected",
          message="Significant volume spike detected - watch for new order blocks")

alertcondition(enableComboAlert and perfect_setup and newBlockIsSupport and bullish_reversal,
          title="Perfect Bullish Setup",
          message="Perfect Bullish Setup - New Support Block + Bullish Reversal")

alertcondition(enableComboAlert and perfect_setup and not newBlockIsSupport and bearish_reversal,
          title="Perfect Bearish Setup", 
          message="Perfect Bearish Setup - New Resistance Block + Bearish Reversal")

alertcondition(enableRetestAlert and confluence_retest_confirmed,
          title="Confluence Zone Retest",
          message="Confluence Zone Retest - High probability reaction expected")

alertcondition(enableCriticalAlert and critical_break and brokenBlockIsSupport,
          title="Critical Support Break",
          message="Critical Support Level Broken - Significant move expected")

alertcondition(enableCriticalAlert and critical_break and not brokenBlockIsSupport,
          title="Critical Resistance Break",
          message="Critical Resistance Level Broken - Significant move expected")

alertcondition(enableQuickAlert and newBlockCreated,
          title="Quick Block Alert",
          message="Quick notification - New order block created")

alertcondition(enableQuickAlert and blockBroken,
          title="Quick Break Alert", 
          message="Quick notification - Order block broken")